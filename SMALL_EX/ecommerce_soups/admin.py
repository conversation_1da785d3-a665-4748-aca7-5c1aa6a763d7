"""Admin panel functionality."""

import streamlit as st
import pandas as pd
from products import (
    get_all_products, add_product, update_product, delete_product,
    get_product_by_id
)
from orders import get_all_orders, update_order_status, get_order_items
from config import CATEGORIES, ORDER_STATUS, SAMPLE_IMAGES
from auth import require_admin

@require_admin
def show_admin_panel():
    """Show admin panel."""
    st.title("🔧 Admin Panel")
    
    tab1, tab2, tab3 = st.tabs(["📦 Products", "📋 Orders", "📊 Analytics"])
    
    with tab1:
        show_product_management()
    
    with tab2:
        show_order_management()
    
    with tab3:
        show_analytics()

def show_product_management():
    """Show product management interface."""
    st.subheader("Product Management")
    
    # Add new product
    with st.expander("➕ Add New Product"):
        with st.form("add_product_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                name = st.text_input("Product Name")
                price = st.number_input("Price ($)", min_value=0.01, step=0.01)
                category = st.selectbox("Category", options=list(CATEGORIES.keys()), 
                                      format_func=lambda x: CATEGORIES[x])
            
            with col2:
                stock = st.number_input("Stock Quantity", min_value=0, step=1)
                image_url = st.selectbox("Sample Image", options=list(SAMPLE_IMAGES.keys()),
                                       format_func=lambda x: x.replace('_', ' ').title())
            
            description = st.text_area("Description")
            
            if st.form_submit_button("Add Product"):
                if name and price > 0:
                    product_id = add_product(
                        name, description, price, category, stock, 
                        SAMPLE_IMAGES[image_url]
                    )
                    st.success(f"Product added successfully! ID: {product_id}")
                    st.rerun()
                else:
                    st.error("Please fill in required fields")
    
    # Display existing products
    st.subheader("Existing Products")
    products = get_all_products()
    
    if products:
        df = pd.DataFrame(products)
        df['category'] = df['category'].map(CATEGORIES)
        
        # Display products in a table
        for idx, product in enumerate(products):
            with st.expander(f"{product['name']} - ${product['price']:.2f}"):
                col1, col2, col3 = st.columns([2, 2, 1])
                
                with col1:
                    st.write(f"**Description:** {product['description']}")
                    st.write(f"**Category:** {CATEGORIES[product['category']]}")
                    st.write(f"**Stock:** {product['stock']}")
                
                with col2:
                    if product['image_url']:
                        st.image(product['image_url'], width=150)
                
                with col3:
                    if st.button(f"Edit", key=f"edit_{product['id']}"):
                        st.session_state[f"edit_product_{product['id']}"] = True
                    
                    if st.button(f"Delete", key=f"delete_{product['id']}"):
                        delete_product(product['id'])
                        st.success("Product deleted!")
                        st.rerun()
                
                # Edit form
                if st.session_state.get(f"edit_product_{product['id']}", False):
                    with st.form(f"edit_form_{product['id']}"):
                        st.write("**Edit Product**")
                        new_name = st.text_input("Name", value=product['name'])
                        new_description = st.text_area("Description", value=product['description'])
                        new_price = st.number_input("Price", value=product['price'], min_value=0.01)
                        new_category = st.selectbox("Category", 
                                                  options=list(CATEGORIES.keys()),
                                                  index=list(CATEGORIES.keys()).index(product['category']),
                                                  format_func=lambda x: CATEGORIES[x])
                        new_stock = st.number_input("Stock", value=product['stock'], min_value=0)
                        
                        col1, col2 = st.columns(2)
                        with col1:
                            if st.form_submit_button("Update"):
                                update_product(product['id'], new_name, new_description, 
                                             new_price, new_category, new_stock, product['image_url'])
                                st.success("Product updated!")
                                st.session_state[f"edit_product_{product['id']}"] = False
                                st.rerun()
                        
                        with col2:
                            if st.form_submit_button("Cancel"):
                                st.session_state[f"edit_product_{product['id']}"] = False
                                st.rerun()
    else:
        st.info("No products found. Add some products to get started!")

def show_order_management():
    """Show order management interface."""
    st.subheader("Order Management")
    
    orders = get_all_orders()
    
    if orders:
        for order in orders:
            with st.expander(f"Order #{order['id']} - {order['username']} - ${order['total_amount']:.2f}"):
                col1, col2 = st.columns([2, 1])
                
                with col1:
                    st.write(f"**Customer:** {order['username']}")
                    st.write(f"**Total:** ${order['total_amount']:.2f}")
                    st.write(f"**Date:** {order['created_at']}")
                    st.write(f"**Status:** {ORDER_STATUS[order['status']]}")
                    
                    # Show order items
                    items = get_order_items(order['id'])
                    if items:
                        st.write("**Items:**")
                        for item in items:
                            st.write(f"- {item['name']} x{item['quantity']} @ ${item['price']:.2f}")
                
                with col2:
                    new_status = st.selectbox(
                        "Update Status",
                        options=list(ORDER_STATUS.keys()),
                        index=list(ORDER_STATUS.keys()).index(order['status']),
                        format_func=lambda x: ORDER_STATUS[x],
                        key=f"status_{order['id']}"
                    )
                    
                    if st.button(f"Update", key=f"update_{order['id']}"):
                        update_order_status(order['id'], new_status)
                        st.success("Order status updated!")
                        st.rerun()
    else:
        st.info("No orders found.")

def show_analytics():
    """Show analytics dashboard."""
    st.subheader("Analytics Dashboard")
    
    orders = get_all_orders()
    products = get_all_products()
    
    if orders and products:
        # Basic metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Orders", len(orders))
        
        with col2:
            total_revenue = sum(order['total_amount'] for order in orders)
            st.metric("Total Revenue", f"${total_revenue:.2f}")
        
        with col3:
            avg_order = total_revenue / len(orders) if orders else 0
            st.metric("Average Order", f"${avg_order:.2f}")
        
        with col4:
            st.metric("Total Products", len(products))
        
        # Order status distribution
        status_counts = {}
        for order in orders:
            status = order['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        if status_counts:
            st.subheader("Order Status Distribution")
            df_status = pd.DataFrame(list(status_counts.items()), 
                                   columns=['Status', 'Count'])
            df_status['Status'] = df_status['Status'].map(ORDER_STATUS)
            st.bar_chart(df_status.set_index('Status'))
        
        # Product stock levels
        st.subheader("Product Stock Levels")
        df_products = pd.DataFrame(products)
        df_products = df_products[['name', 'stock']].sort_values('stock')
        st.bar_chart(df_products.set_index('name'))
        
    else:
        st.info("No data available for analytics.")
