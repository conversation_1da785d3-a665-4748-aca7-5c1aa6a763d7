"""Configuration settings for the ecommerce soup website."""

import os

# Database configuration
DATABASE_PATH = "ecommerce_soups.db"

# Session state keys
SESSION_KEYS = {
    'user_id': 'user_id',
    'username': 'username',
    'is_admin': 'is_admin',
    'cart': 'cart',
    'page': 'page'
}

# Product categories
CATEGORIES = {
    'veg': 'Vegetarian Soups',
    'non_veg': 'Non-Vegetarian Soups'
}

# Order status
ORDER_STATUS = {
    'pending': 'Pending',
    'processing': 'Processing',
    'shipped': 'Shipped',
    'delivered': 'Delivered',
    'cancelled': 'Cancelled'
}

# App configuration
APP_CONFIG = {
    'title': '🍲 Soup Palace - Premium Soup Packets',
    'page_icon': '🍲',
    'layout': 'wide'
}

# Sample product images (placeholder URLs)
SAMPLE_IMAGES = {
    'tomato_soup': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=300',
    'chicken_soup': 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?w=300',
    'mushroom_soup': 'https://images.unsplash.com/photo-1547592180-85f173990554?w=300',
    'vegetable_soup': 'https://images.unsplash.com/photo-1547592166-23ac45744acd?w=300'
}
